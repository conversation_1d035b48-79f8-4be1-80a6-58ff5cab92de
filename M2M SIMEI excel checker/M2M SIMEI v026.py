import pandas as pd
import tkinter as tk
from tkinter import filedialog, messagebox
from datetime import datetime
import os
import configparser
import re
from tkinter import ttk
from openpyxl import load_workbook
from openpyxl.styles import PatternFill
import sys

# Load the settings from settings.cfg
config = configparser.ConfigParser()
config.read('settings.cfg')

# Read settings for Sheet1 and Sheet2 column indices and subtract 1
sheet1_sn_col = int(config['Sheet1']['sn_column']) - 1
sheet1_imei_col = int(config['Sheet1']['imei_column']) - 1
sheet2_sn_col = int(config['Sheet2']['sn_column']) - 1
sheet2_imei_col = int(config['Sheet2']['imei_column']) - 1

def beolvas_cfg(fajlnev):
    cfg_dict = {}
    with open(fajlnev, 'r', encoding='utf-8') as f:
        lines = f.readlines()
        for index, sor in enumerate(lines):
            sor = sor.strip()
            if sor and '=' in sor:
                kulcs, ertek = sor.split('=', 1)
                kulcs = kulcs.strip()
                ertek = ertek.strip()
                cfg_dict[kulcs] = {'ertek': ertek, 'sor': index + 1}
    return cfg_dict

def osszehasonlit_cfg(cfg1, cfg2):
    hianyzik_cfg1 = {}
    hianyzik_cfg2 = {}
    kulonbozo_ertekek = {}
    azonos_ertekek = {}

    osszes_kulcs = set(cfg1.keys()).union(set(cfg2.keys()))

    for kulcs in osszes_kulcs:
        info1 = cfg1.get(kulcs)
        info2 = cfg2.get(kulcs)

        if info1 is None:
            # Key is missing in cfg1, present in cfg2
            hianyzik_cfg1[kulcs] = {
                'ertek': info2['ertek'],
                'sor': info2['sor'],
                'statusz': 'hiányzik cfg1-ben'
            }
        elif info2 is None:
            # Key is missing in cfg2, present in cfg1
            hianyzik_cfg2[kulcs] = {
                'ertek': info1['ertek'],
                'sor': info1['sor'],
                'statusz': 'hiányzik cfg2-ben'
            }
        elif info1['ertek'] != info2['ertek']:
            kulonbozo_ertekek[kulcs] = {
                'ertek1': info1['ertek'],
                'ertek2': info2['ertek'],
                'statusz': 'eltérő'
            }
        else:
            azonos_ertekek[kulcs] = {
                'ertek1': info1['ertek'],
                'ertek2': info2['ertek'],
                'statusz': 'azonos'
            }

    return hianyzik_cfg1, hianyzik_cfg2, kulonbozo_ertekek, azonos_ertekek

def clean_value(value):
    """Eltávolítja az illegális karaktereket az értékből."""
    return re.sub(r'[\x00-\x1F\x7F]', '', value)

def export_to_excel(hianyzik_cfg1, hianyzik_cfg2, kulonbozo_ertekek, azonos_ertekek, cfg1_filename, cfg2_filename, save_path):
    rows = []

    # Eltérő értékek
    for kulcs, info in kulonbozo_ertekek.items():
        rows.append([clean_value(kulcs), clean_value(info['ertek1']), clean_value(info['ertek2']), info['statusz']])

    # Azonos értékek
    for kulcs, info in azonos_ertekek.items():
        rows.append([clean_value(kulcs), clean_value(info['ertek1']), clean_value(info['ertek2']), info['statusz']])

    # Hiányzó kulcsok az első cfg-ben
    for kulcs, info in hianyzik_cfg1.items():
        rows.append([clean_value(kulcs), '-', clean_value(info['ertek']), info['statusz']])

    # Hiányzó kulcsok a második cfg-ben
    for kulcs, info in hianyzik_cfg2.items():
        rows.append([clean_value(kulcs), clean_value(info['ertek']), '-', info['statusz']])

    df = pd.DataFrame(rows, columns=['Kulcs', 'Konfig 1 érték', 'Konfig 2 érték', 'Értékek összehasonlítása'])

    # A fájlneveket a "Konfig 1 érték" és "Konfig 2 érték" oszlop első sorába tesszük
    cfg1_filename_only = os.path.basename(cfg1_filename)
    cfg2_filename_only = os.path.basename(cfg2_filename)

    file_info = pd.DataFrame([['', cfg1_filename_only, cfg2_filename_only, '']], columns=df.columns)

    # Egy összegző sor hozzáadása
    summary_row = pd.DataFrame([['Eltérő értékek száma:', len(kulonbozo_ertekek), '', '']], columns=df.columns)

    # Fájlnév létrehozása az aktuális dátummal és idővel
    timestamp = datetime.now().strftime('%Y_%m_%d_%H.%M.%S')

    # Keressük meg a második konfigurációs fájlban a sorozatszámot (smp.modem_sn)
    cfg2_data = beolvas_cfg(cfg2_filename)
    serial_number = cfg2_data.get('smp.modem_sn', {'ertek': 'snnotfound'})['ertek']

    # Fájlnév sorozatszám használatával, vagy ha nincs, akkor "snnotfound"
    output_file = f'cfg_{serial_number}_{timestamp}.xlsx'

    # Adatok egyesítése: fájlnevek -> összegzés -> összehasonlítás eredménye
    df = pd.concat([file_info, summary_row, df], ignore_index=True)

    # Mentés a megadott könyvtárba vagy az aktuális könyvtárba
    if save_path:
        output_path = os.path.join(save_path, output_file)
    else:
        output_path = os.path.join(os.getcwd(), output_file)

    df.to_excel(output_path, index=False)
    return output_file, df

def excel_col_to_num(col_str):
    """Convert Excel column letter to number."""
    num = 0
    for c in col_str.upper():
        if c.isalpha():
            num = num * 26 + (ord(c) - ord('A')) + 1
    return num


def parse_columns_input(input_str):
    """Parse a string of column identifiers (numbers or letters) into a list of column indices (starting from 1)."""
    cols = set()
    for part in input_str.split(','):
        part = part.strip()
        if not part:
            continue
        if '-' in part:
            start_str, end_str = part.split('-', 1)
            start_str = start_str.strip()
            end_str = end_str.strip()
            if start_str.isdigit():
                start_idx = int(start_str)
            else:
                start_idx = excel_col_to_num(start_str)
            if end_str.isdigit():
                end_idx = int(end_str)
            else:
                end_idx = excel_col_to_num(end_str)
            cols.update(range(start_idx, end_idx + 1))
        else:
            if part.isdigit():
                cols.add(int(part))
            else:
                cols.add(excel_col_to_num(part))
    return sorted(cols)

def parse_column_pairs_input(input_str):
    """Parse a string of column pairs (numbers or letters) into a list of tuples of column indices."""
    pairs = []
    for pair_str in input_str.split(';'):
        pair_str = pair_str.strip()
        if not pair_str:
            continue
        cols = pair_str.split(',')
        if len(cols) != 2:
            raise ValueError("Az összehasonlítandó oszlopok párokat kell alkossanak (pl. 3,4 vagy A,B).")
        col1 = cols[0].strip()
        col2 = cols[1].strip()
        if col1.isdigit():
            col1_idx = int(col1)
        else:
            col1_idx = excel_col_to_num(col1)
        if col2.isdigit():
            col2_idx = int(col2)
        else:
            col2_idx = excel_col_to_num(col2)
        pairs.append((col1_idx, col2_idx))
    return pairs

def get_special_search_options():
    options_window = tk.Toplevel()
    options_window.title("Speciális keresés beállításai")
    options_window.resizable(False, False)
    # Variables to store user inputs
    starting_row_var = tk.StringVar(value="2")
    duplicate_columns_var = tk.StringVar()
    difference_columns_var = tk.StringVar()
    compare_columns_pairs_var = tk.StringVar()

    # Create a grid layout with padding
    for i in range(5):
        options_window.rowconfigure(i, pad=10)
    for i in range(2):
        options_window.columnconfigure(i, pad=10)

    # Create input fields with labels
    tk.Label(options_window, text="Kezdő sor:").grid(row=0, column=0, sticky='e')
    starting_row_entry = tk.Entry(options_window, textvariable=starting_row_var, width=40)
    starting_row_entry.grid(row=0, column=1)

    tk.Label(options_window, text="Duplikátumot kereső oszlopok (pl. 1-4,6 vagy A-D,F):").grid(row=1, column=0, sticky='e')
    duplicate_columns_entry = tk.Entry(options_window, textvariable=duplicate_columns_var, width=40)
    duplicate_columns_entry.grid(row=1, column=1)

    tk.Label(options_window, text="Eltérést kereső oszlopok (pl. 1-4 vagy A-D):").grid(row=2, column=0, sticky='e')
    difference_columns_entry = tk.Entry(options_window, textvariable=difference_columns_var, width=40)
    difference_columns_entry.grid(row=2, column=1)

    tk.Label(options_window, text="Oszlopok összehasonlítása (pl. 3,4;5,6 vagy C,D;E,F):").grid(row=3, column=0, sticky='e')
    compare_columns_pairs_entry = tk.Entry(options_window, textvariable=compare_columns_pairs_var, width=40)
    compare_columns_pairs_entry.grid(row=3, column=1)

    result = {}
    def on_ok():
        try:
            starting_row = int(starting_row_var.get())
            duplicate_columns = parse_columns_input(duplicate_columns_var.get())
            difference_columns = parse_columns_input(difference_columns_var.get())
            compare_columns_pairs_input = compare_columns_pairs_var.get()
            compare_columns_pairs = []
            if compare_columns_pairs_input:
                compare_columns_pairs = parse_column_pairs_input(compare_columns_pairs_input)
            result['starting_row'] = starting_row
            result['duplicate_columns'] = duplicate_columns
            result['difference_columns'] = difference_columns
            result['compare_columns_pairs'] = compare_columns_pairs
            options_window.destroy()
        except ValueError as e:
            messagebox.showerror("Hiba", f"Kérjük, adjon meg érvényes oszlopazonosítókat.\n{e}")

    def on_cancel():
        options_window.destroy()
        result.clear()

    # Buttons frame
    buttons_frame = tk.Frame(options_window)
    buttons_frame.grid(row=4, column=0, columnspan=2, pady=10)

    tk.Button(buttons_frame, text="OK", command=on_ok, width=15).pack(side=tk.LEFT, padx=5)
    tk.Button(buttons_frame, text="Mégse", command=on_cancel, width=15).pack(side=tk.RIGHT, padx=5)

    options_window.wait_window()
    if result:
        return result
    else:
        return None

def process_excel_file():
    global last_log_file, last_excel_file, total_elements_label, imei_mismatches_label, column_mismatches_label, duplicate_sns_label

    # Fájl kiválasztása fájldialogus segítségével
    file_path = filedialog.askopenfilename(filetypes=[("Excel files", "*.xls *.xlsx *.csv")])

    if not file_path:
        print("Nincs fájl kiválasztva.")
        return

    # Speciális keresés megkérdezése
    special_search = messagebox.askyesno("Speciális keresés", "Szeretne speciális keresést végezni?")

    if special_search:
        options = get_special_search_options()
        if options is None:
            # User cancelled
            return
        starting_row = options['starting_row']
        duplicate_columns = options['duplicate_columns']
        difference_columns = options['difference_columns']
        compare_columns_pairs = options['compare_columns_pairs']
    else:
        starting_row = None
        duplicate_columns = None
        difference_columns = None
        compare_columns_pairs = None

    try:
        # Ellenőrizzük, hogy a fájl CSV-e, ha nem, akkor ellenőrizzük a munkalapok számát
        if file_path.lower().endswith('.csv'):
            is_only_one_sheet = True
        else:
            # Az ExcelFile osztállyal megnyitjuk a fájlt, és megszámoljuk a munkalapokat
            excel_file = pd.ExcelFile(file_path)
            if len(excel_file.sheet_names) == 1:
                is_only_one_sheet = True
            else:
                is_only_one_sheet = False

        # Ha csak egy munkalap van vagy CSV fájl
        if is_only_one_sheet:
            # Olvassuk be a fájlt
            file_extension = os.path.splitext(file_path)[1].lower()
            if file_extension == '.csv':
                df1 = pd.read_csv(file_path, sep=';', dtype=str)
            elif file_extension in ['.xls', '.xlsx']:
                df1 = pd.read_excel(file_path, sheet_name=0, dtype=str)
            else:
                print("Nem támogatott fájlformátum.")
                return

            # Ha meg van adva a kezdő sor, akkor levágjuk a dataframe elejét
            if starting_row:
                df1 = df1.iloc[starting_row - 2:]  # Fejléc után kezdődik az adatok indexelése

            # Konvertáljuk az összes oszlopot szöveggé
            df1 = df1.astype(str)

            # Fájl mentése Excel formátumban
            now = datetime.now().strftime("%Y_%m_%d_%H.%M.%S")
            original_file_name = os.path.basename(file_path).split('.')[0]
            new_file_name = f"{now}_{original_file_name}.xlsx"

            if save_path_entry.get():
                save_dir = save_path_entry.get()
                new_file_path = os.path.join(save_dir, new_file_name)
            else:
                new_file_path = new_file_name

            df1.to_excel(new_file_path, index=False)
            print(f"Fájl elmentve: {new_file_path}")

            # További feldolgozások (pl. log fájl létrehozása, cella formázás, stb.)
            log_file_name, total_mismatches, total_column_discrepancies, num_duplicates, cells_to_highlight = create_log_file(
                new_file_path, len(df1), now, original_file_name, starting_row, duplicate_columns, difference_columns, compare_columns_pairs)

            # Töltsük be a munkafüzetet és formázzuk a cellákat
            wb = load_workbook(new_file_path)
            ws = wb.active

            # Az összes cella formátumának beállítása szövegesre
            for row in ws.iter_rows(min_row=2, max_row=ws.max_row, min_col=1, max_col=ws.max_column):
                for cell in row:
                    cell.number_format = '@'  # Szöveg formátum

            # Piros kitöltési stílus létrehozása a hibás cellákhoz
            red_fill = PatternFill(start_color='FFFF0000', end_color='FFFF0000', fill_type='solid')

            # Munkafüzet oszlopainak leképezése
            header = [cell.value for cell in ws[1]]
            column_name_to_index = {name: idx for idx, name in enumerate(header, start=1)}  # 1-alapú index

            # Hibás cellák pirossal való megjelölése
            for row_idx, col_name in cells_to_highlight:
                col_idx = column_name_to_index.get(col_name)
                if col_idx:
                    cell = ws.cell(row=row_idx, column=col_idx)
                    cell.fill = red_fill

            # Munkafüzet mentése
            wb.save(new_file_path)

            last_excel_file = new_file_path
            last_log_file = log_file_name

            # Címkék frissítése
            total_elements_label.config(text=f"Összes elem: {len(df1)}")
            imei_mismatches_label.config(text=f"IMEI eltérések száma: {total_mismatches}")
            column_mismatches_label.config(text=f"Oszlop eltérések száma: {total_column_discrepancies}")
            duplicate_sns_label.config(text=f"Duplikált SN-ek száma: {num_duplicates}")

            # Siker üzenet megjelenítése
            messagebox.showinfo("Siker", f"Fájl elmentve: {new_file_path}\nLog fájl: {log_file_name}")

        else:
            # Ha több munkalap van, az eredeti feldolgozást használjuk
            file_extension = os.path.splitext(file_path)[1].lower()

            if file_extension == '.xls':
                df1 = pd.read_excel(file_path, sheet_name=0, engine='xlrd', header=None, dtype=str)
                df2 = pd.read_excel(file_path, sheet_name=1, engine='xlrd', dtype=str)
            elif file_extension == '.xlsx':
                df1 = pd.read_excel(file_path, sheet_name=0, engine='openpyxl', header=None, dtype=str)
                df2 = pd.read_excel(file_path, sheet_name=1, engine='openpyxl', dtype=str)
            else:
                print("Nem támogatott fájlformátum.")
                return

            # Ha meg van adva a kezdő sor, akkor levágjuk a dataframe elejét
            if starting_row:
                df1 = df1.iloc[starting_row - 2:]
                df2 = df2.iloc[starting_row - 2:]

            # Konvertáljuk az összes oszlopot szöveggé
            df1 = df1.astype(str)
            df2 = df2.astype(str)

            # Get the total number of rows in the first sheet (excluding header row)
            total_rows = len(df1)

            # Manually set column names for the first sheet
            df1.columns = ['Elemek száma:', str(total_rows), 'SN', 'IMEI_1'] + list(df1.columns[4:])

            # Ensure SN and IMEI columns are treated as strings
            col_df1_sn = df1.columns[sheet1_sn_col]
            col_df1_imei = df1.columns[sheet1_imei_col]
            col_df2_sn = df2.columns[sheet2_sn_col]
            col_df2_imei = df2.columns[sheet2_imei_col]

            df1[col_df1_sn] = df1[col_df1_sn].astype(str)
            df1[col_df1_imei] = df1[col_df1_imei].astype(str)
            df2[col_df2_sn] = df2[col_df2_sn].astype(str)
            df2[col_df2_imei] = df2[col_df2_imei].astype(str)

            # Rename SN and IMEI columns to standard names
            df1.rename(columns={col_df1_sn: 'SN', col_df1_imei: 'IMEI_df1'}, inplace=True)
            df2.rename(columns={col_df2_sn: 'SN', col_df2_imei: 'IMEI_df2'}, inplace=True)

            # Merge on SN only, using left join to keep all rows from df1
            merged_df = pd.merge(
                df1,
                df2,
                on='SN',
                how='left',
                suffixes=('_df1', '_df2')
            )

            # Ha meg van adva a kezdő sor, akkor levágjuk a merged_df elejét
            if starting_row:
                merged_df = merged_df.iloc[starting_row - 2:]

            # Konvertáljuk az összes oszlopot szöveggé
            merged_df = merged_df.astype(str)

            # Save the resulting dataframe with the current date and time
            now = datetime.now().strftime("%Y_%m_%d_%H.%M.%S")
            original_file_name = os.path.basename(file_path).split('.')[0]
            new_file_name = f"{now}_{original_file_name}.xlsx"

            # Mentés a megadott könyvtárba vagy az aktuális könyvtárba
            if save_path_entry.get():
                save_dir = save_path_entry.get()
                new_file_path = os.path.join(save_dir, new_file_name)
            else:
                new_file_path = new_file_name

            merged_df.to_excel(new_file_path, index=False)
            print(f"Fájl elmentve: {new_file_path}")

            # --- Start of the code added for highlighting duplicated SNs ---

            # Find duplicated SNs in merged_df
            if duplicate_columns:
                duplicate_column_names = [merged_df.columns[i - 1] for i in duplicate_columns if i - 1 < len(merged_df.columns)]
            else:
                duplicate_column_names = ['SN']

            duplicated_rows = merged_df.duplicated(subset=duplicate_column_names, keep=False)
            duplicated_indices = merged_df.index[duplicated_rows].tolist()

            # Load the workbook
            wb = load_workbook(new_file_path)
            ws = wb.active

            # Create a red fill style
            red_fill = PatternFill(start_color='FFFF0000',
                                   end_color='FFFF0000',
                                   fill_type='solid')

            # For each duplicated row, apply red fill
            for idx in duplicated_indices:
                excel_row = idx + 2  # Adjust for header row in Excel
                for cell in ws[excel_row]:
                    cell.fill = red_fill

            # --- End of the code added for highlighting duplicated SNs ---

            # Call the log creation function
            log_file_name, total_mismatches, total_column_discrepancies, num_duplicates, cells_to_highlight = create_log_file(
                new_file_path, len(df1), now, original_file_name, starting_row, duplicate_columns, difference_columns, compare_columns_pairs)

            # --- Start of the code added for highlighting discrepancies ---

            # Create a mapping from column names to column indices
            header = [cell.value for cell in ws[1]]
            column_name_to_index = {name: idx for idx, name in enumerate(header, start=1)}  # 1-based index

            # For each cell to highlight, apply red fill
            for row_idx, col_name in cells_to_highlight:
                col_idx = column_name_to_index.get(col_name)
                if col_idx:
                    cell = ws.cell(row=row_idx, column=col_idx)
                    cell.fill = red_fill

            # Save the workbook with the red-filled discrepancies
            wb.save(new_file_path)

            # --- End of the code added for highlighting discrepancies ---

            last_excel_file = new_file_path
            last_log_file = log_file_name

            # Update the labels
            total_elements_label.config(text=f"Összes elem: {len(df1)}")
            imei_mismatches_label.config(text=f"IMEI eltérések száma: {total_mismatches}")
            column_mismatches_label.config(text=f"Oszlop eltérések száma: {total_column_discrepancies}")
            duplicate_sns_label.config(text=f"Duplikált SN-ek száma: {num_duplicates}")

            # Display success message
            messagebox.showinfo("Siker", f"Fájl elmentve: {new_file_path}\nLog fájl: {log_file_name}")

    except Exception as e:
        print(f"Hiba történt: {e}")
        messagebox.showerror("Hiba", f"Hiba történt: {e}")

def create_log_file(excel_file_name, df1_length, now, original_file_name, starting_row=None, duplicate_columns=None, difference_columns=None, compare_columns_pairs=None):
    # Read the Excel file that was just created
    merged_df = pd.read_excel(excel_file_name, dtype=str)

    # Ha meg van adva a kezdő sor, akkor levágjuk a merged_df elejét
    if starting_row:
        merged_df = merged_df.iloc[starting_row - 2:]  # Fejléc után kezdődik az adatok indexelése

    # Read the column exceptions from dont_check_column.txt
    try:
        with open('dont_check_column.txt', 'r', encoding='utf-8') as f:
            excluded_columns = [line.strip() for line in f if line.strip()]
    except FileNotFoundError:
        excluded_columns = []
        print("dont_check_column.txt fájl nem található. Nincsenek oszlopkivétel beállítások.")

    # Read the columns to check for duplicates from check_duplicate_row.txt
    try:
        with open('check_duplicate_row.txt', 'r', encoding='utf-8') as f:
            duplicate_columns_from_file = [line.strip() for line in f if line.strip()]
    except FileNotFoundError:
        duplicate_columns_from_file = []
        print("check_duplicate_row.txt fájl nem található. Nincsenek további duplikált oszlopok.")

    # Create log file based on the generated Excel file
    log_file_name = f"{now}_{original_file_name}.log"
    total_mismatches = 0
    per_row_differences = []
    cells_to_highlight = []  # List of tuples (row_index, column_name)

    # Initialize num_duplicates
    num_duplicates = 0

    # Initialize log content as a list
    log_lines = []
    # Placeholder for summary; we'll update these values later
    log_lines.append(f"Összes elem: {df1_length}\n")
    log_lines.append(f"IMEI eltérések száma: {total_mismatches}\n")
    log_lines.append(f"Oszlop eltérések száma: 0\n")
    log_lines.append(f"Duplikált értékek száma: {num_duplicates}\n\n")

    # Check for duplicate values
    if duplicate_columns:
        duplicate_column_names = [merged_df.columns[i - 1] for i in duplicate_columns if i - 1 < len(merged_df.columns)]
    else:
        duplicate_column_names = []
        if 'SN' in merged_df.columns:
            duplicate_column_names.append('SN')
        if 'IMEI' in merged_df.columns:
            duplicate_column_names.append('IMEI')
        if 'WMR_SN' in merged_df.columns:
            duplicate_column_names.append('WMR_SN')
        # Add columns from check_duplicate_row.txt
        for col_name in duplicate_columns_from_file:
            if col_name in merged_df.columns and col_name not in duplicate_column_names:
                duplicate_column_names.append(col_name)

    for col in duplicate_column_names:
        duplicated_col = merged_df[col].duplicated(keep=False)
        num_col_duplicates = duplicated_col.sum()
        if num_col_duplicates > 0:
            num_duplicates += num_col_duplicates
            log_lines.append(f"Duplikált '{col}' értékek találhatók az alábbi sorokban:\n")
            duplicated_indices = merged_df.index[duplicated_col].tolist()
            for idx in duplicated_indices:
                row = merged_df.loc[idx]
                value = row[col]
                log_lines.append(f"Sor {idx + 2}: {col} '{value}'\n")  # +2 to adjust for Excel row numbering
                # Highlight the duplicate cell
                cells_to_highlight.append((idx + 2, col))  # +2 for Excel row numbering
            log_lines.append("\n")

    # Now that we have num_duplicates, update the summary
    log_lines[3] = f"Duplikált értékek száma: {num_duplicates}\n\n"

    if 'IMEI_df2' in merged_df.columns and 'SN' in merged_df.columns:
        for index, row in merged_df.iterrows():
            imei_df1 = row['IMEI_df1']
            imei_df2 = row.get('IMEI_df2', None)
            if pd.isna(imei_df2):
                # SN not found in df2
                per_row_differences.append(f"Sor {index + 2}: SN '{row['SN']}' nem található a második lapon.\n")
                # Highlight the 'SN' cell
                cells_to_highlight.append((index + 2, 'SN'))  # +2 for Excel row numbering
            elif imei_df1 != imei_df2:
                total_mismatches += 1
                per_row_differences.append(
                    f"Sor {index + 2}: SN '{row['SN']}', IMEI eltérés: '{imei_df1}' != '{imei_df2}'\n")
                # Highlight the 'IMEI_df1' and 'IMEI_df2' cells
                cells_to_highlight.append((index + 2, 'IMEI_df1'))
                cells_to_highlight.append((index + 2, 'IMEI_df2'))
    else:
        log_lines.append("Csak egy lap található vagy 'IMEI_df2' nem létezik, IMEI összehasonlítás kihagyva.\n")

    log_lines.extend(per_row_differences)

    # --- Start of the new code for column comparisons ---
    # Get the columns to compare
    if difference_columns:
        columns_to_compare = [merged_df.columns[i - 1] for i in difference_columns if i - 1 < len(merged_df.columns)]
    else:
        # Default behavior: compare from 4th column onwards
        columns_to_compare = merged_df.columns[3:]

    # Exclude columns from dont_check_column.txt
    columns_to_compare = [col for col in columns_to_compare if col not in excluded_columns]

    log_lines.append("\nOszloponkénti összehasonlítás:\n\n")

    total_column_discrepancies = 0  # New variable to keep track of total discrepancies

    for col in columns_to_compare:
        col_values = merged_df[col].reset_index(drop=True)
        # Try to get the first non-empty value
        first_element = None
        first_element_index = None
        for idx, val in enumerate(col_values):
            if pd.notna(val) and val != '':
                first_element = val
                first_element_index = idx
                break
        if first_element is None:
            # All elements are NaN or empty
            log_lines.append(f"A(z) '{col}' oszlop összes mezője üres.\n")
            continue
        else:
            # If first element is not the first row, log that the previous elements were empty
            if first_element_index > 0:
                log_lines.append(f"A(z) '{col}' oszlop első mezője üres volt.\n")
            # Log the value used for comparison
            log_lines.append(f"A(z) '{col}' oszlop összehasonlítása a(z) '{first_element}' értékkel.\n")
        # Compare the first element with all other elements
        discrepancies = []
        for idx, val in enumerate(col_values):
            # Skip the reference element itself
            if idx == first_element_index:
                continue
            # Consider both NaN and empty strings as empty
            if pd.isna(val) or val == '':
                val = ''
            if val != first_element:
                discrepancies.append(f"Sor {merged_df.index[idx] + 2}: eltérő érték: '{val}'")  # +2 for Excel row numbering
                # Record cell to highlight
                cells_to_highlight.append((merged_df.index[idx] + 2, col))
        if discrepancies:
            total_column_discrepancies += len(discrepancies)
            log_lines.append(f"A(z) '{col}' oszlopban eltérések találhatók az alábbi sorokban:\n")
            for discrepancy in discrepancies:
                log_lines.append(f"{discrepancy}\n")
        else:
            log_lines.append(f"A(z) '{col}' oszlopban nincs eltérés.\n")

    # --- New code for comparing column pairs ---
    if compare_columns_pairs:
        log_lines.append("\nOszlop párok összehasonlítása:\n\n")
        for col1_idx, col2_idx in compare_columns_pairs:
            if col1_idx - 1 < len(merged_df.columns) and col2_idx - 1 < len(merged_df.columns):
                col1_name = merged_df.columns[col1_idx - 1]
                col2_name = merged_df.columns[col2_idx - 1]
                log_lines.append(f"'{col1_name}' és '{col2_name}' oszlopok összehasonlítása soronként:\n")
                discrepancies = []
                for idx, row in merged_df.iterrows():
                    val1 = row[col1_name]
                    val2 = row[col2_name]
                    if pd.isna(val1):
                        val1 = ''
                    if pd.isna(val2):
                        val2 = ''
                    if val1 != val2:
                        discrepancies.append(f"Sor {idx + 2}: '{col1_name}'='{val1}' != '{col2_name}'='{val2}'")
                        # Record cells to highlight
                        cells_to_highlight.append((idx + 2, col1_name))
                        cells_to_highlight.append((idx + 2, col2_name))
                if discrepancies:
                    total_column_discrepancies += len(discrepancies)
                    for discrepancy in discrepancies:
                        log_lines.append(f"{discrepancy}\n")
                else:
                    log_lines.append("Nincs eltérés az oszlopok között.\n")
            else:
                log_lines.append(f"Hiba: Az egyik oszlop indexe túl nagy: {col1_idx}, {col2_idx}\n")

    # --- End of the new code ---

    # Now that we have total_mismatches and total_column_discrepancies, update the summary
    log_lines[1] = f"IMEI eltérések száma: {total_mismatches}\n"
    log_lines[2] = f"Oszlop eltérések száma: {total_column_discrepancies}\n"
    log_lines[3] = f"Duplikált értékek száma: {num_duplicates}\n\n"

    # Mentés a megadott könyvtárba vagy az aktuális könyvtárba
    if save_path_entry.get():
        save_dir = save_path_entry.get()
        log_file_path = os.path.join(save_dir, log_file_name)
    else:
        log_file_path = log_file_name

    # Write the log file
    with open(log_file_path, 'w', encoding='utf-8') as log_file:
        log_file.writelines(log_lines)

    return log_file_path, total_mismatches, total_column_discrepancies, num_duplicates, cells_to_highlight


def open_settings():
    # Create a new window
    settings_window = tk.Toplevel()
    settings_window.title("Beállítások")

    def save_settings():
        # Save the settings to settings.cfg
        config['Sheet1']['sn_column'] = sheet1_sn_entry.get()
        config['Sheet1']['imei_column'] = sheet1_imei_entry.get()
        config['Sheet2']['sn_column'] = sheet2_sn_entry.get()
        config['Sheet2']['imei_column'] = sheet2_imei_entry.get()

        with open('settings.cfg', 'w') as configfile:
            config.write(configfile)

        messagebox.showinfo("Beállítások", "Beállítások elmentve.")
        settings_window.destroy()

    # Sheet1 settings
    tk.Label(settings_window, text="Sheet1 SN oszlop index:").grid(row=0, column=0, sticky='e')
    sheet1_sn_entry = tk.Entry(settings_window)
    sheet1_sn_entry.insert(0, str(int(config['Sheet1']['sn_column'])))
    sheet1_sn_entry.grid(row=0, column=1)

    tk.Label(settings_window, text="Sheet1 IMEI oszlop index:").grid(row=1, column=0, sticky='e')
    sheet1_imei_entry = tk.Entry(settings_window)
    sheet1_imei_entry.insert(0, str(int(config['Sheet1']['imei_column'])))
    sheet1_imei_entry.grid(row=1, column=1)

    # Sheet2 settings
    tk.Label(settings_window, text="Sheet2 SN oszlop index:").grid(row=2, column=0, sticky='e')
    sheet2_sn_entry = tk.Entry(settings_window)
    sheet2_sn_entry.insert(0, str(int(config['Sheet2']['sn_column'])))
    sheet2_sn_entry.grid(row=2, column=1)

    tk.Label(settings_window, text="Sheet2 IMEI oszlop index:").grid(row=3, column=0, sticky='e')
    sheet2_imei_entry = tk.Entry(settings_window)
    sheet2_imei_entry.insert(0, str(int(config['Sheet2']['imei_column'])))
    sheet2_imei_entry.grid(row=3, column=1)



    # Functions to open the text files
    def open_ignore_columns():
        if os.path.exists('dont_check_column.txt'):
            os.startfile('dont_check_column.txt')
        else:
            with open('dont_check_column.txt', 'w', encoding='utf-8') as f:
                pass  # create empty file
            os.startfile('dont_check_column.txt')

    def open_check_duplicate_rows():
        if os.path.exists('check_duplicate_row.txt'):
            os.startfile('check_duplicate_row.txt')
        else:
            with open('check_duplicate_row.txt', 'w', encoding='utf-8') as f:
                pass  # create empty file
            os.startfile('check_duplicate_row.txt')

    # Buttons to open the text files
    ignore_columns_button = tk.Button(settings_window, text="Ignore Columns", command=open_ignore_columns)
    ignore_columns_button.grid(row=4, column=0, columnspan=2, pady=5)

    check_duplicate_rows_button = tk.Button(settings_window, text="Check Duplicate Rows", command=open_check_duplicate_rows)
    check_duplicate_rows_button.grid(row=5, column=0, columnspan=2, pady=5)

    # Save button
    save_button = tk.Button(settings_window, text="Mentés", command=save_settings)
    save_button.grid(row=6, column=0, columnspan=2, pady=20)

    settings_window.mainloop()


# Create a simple GUI to select an Excel file and process it
def create_gui():
    global total_elements_label, imei_mismatches_label, column_mismatches_label, duplicate_sns_label, only_one_sheet_var, save_path_entry
    root = tk.Tk()
    root.title("M2M SIMEI Checker v26")
    root.geometry('600x600')  # Megnövelt ablakméret

    # --- MX Config Checker integrációja ---

    # Frame az MX Config Checker számára (most legfelül)
    mx_frame = tk.LabelFrame(root, text='Config Checker')
    mx_frame.pack(padx=10, pady=10, fill='both', expand=True)

    cfg1_path = tk.StringVar()
    cfg2_path = tk.StringVar()
    last_comparison = None  # Utolsó összehasonlítás eredménye
    last_file_name = ''  # Mentett fájl neve

    def valaszd_ki_elso_cfg():
        fajl = filedialog.askopenfilename(title='Első CFG fájl kiválasztása',
                                          filetypes=[('CFG Files', '*.cfg'), ('All Files', '*.*')])
        if fajl:
            cfg1_path.set(fajl)

    def valaszd_ki_masodik_cfg():
        fajl = filedialog.askopenfilename(title='Második CFG fájl kiválasztása',
                                          filetypes=[('CFG Files', '*.cfg'), ('All Files', '*.*')])
        if fajl:
            cfg2_path.set(fajl)

    def uj_osszehasonlitas():
        nonlocal last_comparison, last_file_name

        # Változók alaphelyzetbe állítása
        cfg1_path.set('')
        cfg2_path.set('')
        last_comparison = None
        last_file_name = ''

        # Címkék törlése
        result_label.config(text='')
        success_label.config(text='')

        # Gomb visszaállítása eredeti állapotba
        compare_button.config(text='Összehasonlítás', command=osszehasonlit)

    def osszehasonlit():
        nonlocal last_comparison, last_file_name

        # Szürkére állítjuk a gombot
        compare_button.config(state=tk.DISABLED)

        if not cfg1_path.get() or not cfg2_path.get():
            messagebox.showwarning('Figyelmeztetés', 'Kérjük, válassza ki mindkét CFG fájlt.')
            compare_button.config(state=tk.NORMAL)
            return

        cfg1 = beolvas_cfg(cfg1_path.get())
        cfg2 = beolvas_cfg(cfg2_path.get())

        hianyzik_cfg1, hianyzik_cfg2, kulonbozo_ertekek, azonos_ertekek = osszehasonlit_cfg(cfg1, cfg2)

        elt_ertekek_szama = len(kulonbozo_ertekek)

        # Eredmények kiírása a címkékbe
        result_label.config(text=f'Eltérő sorok száma: {elt_ertekek_szama}')

        # Eredmények exportálása Excelbe
        last_file_name, last_comparison = export_to_excel(hianyzik_cfg1, hianyzik_cfg2, kulonbozo_ertekek,
                                                          azonos_ertekek, cfg1_path.get(), cfg2_path.get(), save_path_entry.get())

        success_label.config(text=f'Sikeres összehasonlítás és mentés! Fájl neve: {last_file_name}')

        # Change the button to 'Nyisd meg a mappát' and set the command to open_folder
        compare_button.config(text='Nyisd meg a mappát', command=open_folder)

        # 1 másodperc késleltetés után engedélyezze újra a gombot
        root.after(1000, lambda: compare_button.config(state=tk.NORMAL))

    def open_folder():
        if getattr(sys, 'frozen', False):
            # If the application is frozen (compiled to exe)
            application_path = os.path.dirname(sys.executable)
        else:
            # If not frozen, get the directory of the script
            application_path = os.path.dirname(os.path.abspath(__file__))

        os.startfile(application_path)

    def mutasd_eredmeny():
        if last_comparison is None:
            messagebox.showinfo('Nincs adat', 'Még nem történt összehasonlítás.')
            return

        # Új ablak az eredmények megjelenítéséhez
        eredmeny_window = tk.Toplevel(root)
        eredmeny_window.title('Utolsó összehasonlítás eredménye')

        # Táblázat létrehozása az eredmények megjelenítésére
        tree = ttk.Treeview(eredmeny_window,
                            columns=('Kulcs', 'Konfig 1 érték', 'Konfig 2 érték', 'Értékek összehasonlítása'),
                            show='headings')
        tree.heading('Kulcs', text='Kulcs')
        tree.heading('Konfig 1 érték', text='Konfig 1 érték')
        tree.heading('Konfig 2 érték', text='Konfig 2 érték')
        tree.heading('Értékek összehasonlítása', text='Értékek összehasonlítása')

        # Oszlop szélességének beállítása
        tree.column('Kulcs', width=200)
        tree.column('Konfig 1 érték', width=200)
        tree.column('Konfig 2 érték', width=200)
        tree.column('Értékek összehasonlítása', width=150)

        tree.pack(fill=tk.BOTH, expand=True)

        # Az utolsó összehasonlítás eredményeinek betöltése a táblázatba
        for _, row in last_comparison.iterrows():
            tree.insert('', tk.END, values=(
                row['Kulcs'], row['Konfig 1 érték'], row['Konfig 2 érték'], row['Értékek összehasonlítása']))

    frame = tk.Frame(mx_frame)
    frame.pack(padx=10, pady=10)

    tk.Label(frame, text='Első CFG fájl:').grid(row=0, column=0, sticky='e')
    tk.Entry(frame, textvariable=cfg1_path, width=50).grid(row=0, column=1)
    tk.Button(frame, text='Tallózás...', command=valaszd_ki_elso_cfg).grid(row=0, column=2)

    tk.Label(frame, text='Második CFG fájl:').grid(row=1, column=0, sticky='e')
    tk.Entry(frame, textvariable=cfg2_path, width=50).grid(row=1, column=1)
    tk.Button(frame, text='Tallózás...', command=valaszd_ki_masodik_cfg).grid(row=1, column=2)

    # Gombok kerete
    buttons_frame = tk.Frame(mx_frame)
    buttons_frame.pack(pady=10)

    # Összehasonlítás gomb
    compare_button = tk.Button(buttons_frame, text='Összehasonlítás', command=osszehasonlit)
    compare_button.pack(side=tk.LEFT, padx=5)

    # Új összehasonlítás gomb
    new_comparison_button = tk.Button(buttons_frame, text='Új Összehasonlítás', command=uj_osszehasonlitas)
    new_comparison_button.pack(side=tk.LEFT, padx=5)

    # Címkék az eredmények megjelenítésére
    result_label = tk.Label(mx_frame, text='')
    result_label.pack()

    success_label = tk.Label(mx_frame, text='')
    success_label.pack()

    # Gomb az utolsó összehasonlítás megjelenítésére
    tk.Button(mx_frame, text='Utolsó összehasonlítás eredményeinek megtekintése', command=mutasd_eredmeny).pack(pady=10)

    # --- Excel Checker keret létrehozása ---
    excel_frame = tk.LabelFrame(root, text='Excel Checker')
    excel_frame.pack(padx=10, pady=10, fill='both', expand=True)

    # A fájl kiválasztó és feldolgozó gomb az Excel Checker keretben
    process_button = tk.Button(excel_frame, text="Excel Fájl Kiválasztása és Feldolgozása", command=process_excel_file)
    process_button.pack(pady=10)

    # Üres sor beszúrása
    tk.Label(excel_frame, text="").pack()

    # A négy értékhez tartozó címkék az Excel Checker keretben
    total_elements_label = tk.Label(excel_frame, text="Összes elem: ")
    total_elements_label.pack()
    imei_mismatches_label = tk.Label(excel_frame, text="IMEI eltérések száma: ")
    imei_mismatches_label.pack()
    column_mismatches_label = tk.Label(excel_frame, text="Oszlop eltérések száma: ")
    column_mismatches_label.pack()
    duplicate_sns_label = tk.Label(excel_frame, text="Duplikált SN-ek száma: ")
    duplicate_sns_label.pack()

    # Új keret a gombok számára
    button_frame = tk.Frame(excel_frame)
    button_frame.pack(pady=10)

    def open_last_excel():
        if 'last_excel_file' in globals() and last_excel_file:
            try:
                os.startfile(last_excel_file)
            except Exception as e:
                messagebox.showerror("Hiba", f"A fájl megnyitása sikertelen: {e}")
        else:
            messagebox.showinfo("Információ", "Nincs megnyitható Excel fájl.")

    def open_last_log():
        if 'last_log_file' in globals() and last_log_file:
            try:
                os.startfile(last_log_file)
            except Exception as e:
                messagebox.showerror("Hiba", f"A fájl megnyitása sikertelen: {e}")
        else:
            messagebox.showinfo("Információ", "Nincs megnyitható log fájl.")

    open_excel_button = tk.Button(button_frame, text="Utolsó Excel fájl megnyitása", command=open_last_excel)
    open_excel_button.pack(side=tk.LEFT, padx=5)

    open_log_button = tk.Button(button_frame, text="Utolsó log fájl megnyitása", command=open_last_log)
    open_log_button.pack(side=tk.LEFT, padx=5)

    # Alsó keret a gombok számára
    bottom_frame = tk.Frame(root)
    bottom_frame.pack(side=tk.BOTTOM, fill=tk.X)

    # Mentési út gomb és input mező
    tk.Label(bottom_frame, text="Mentési út:").pack(side=tk.LEFT, padx=5)

    save_path_entry = tk.Entry(bottom_frame, width=40)
    save_path_entry.pack(side=tk.LEFT, padx=5)

    def select_save_path():
        path = filedialog.askdirectory()
        if path:
            save_path_entry.delete(0, tk.END)
            save_path_entry.insert(0, path)

    save_path_button = tk.Button(bottom_frame, text="Tallózás...", command=select_save_path)
    save_path_button.pack(side=tk.LEFT, padx=5)

    # Beállítások gomb
    settings_button = tk.Button(bottom_frame, text="Excel Checker Beállítások", command=open_settings)
    settings_button.pack(side=tk.RIGHT, padx=10, pady=10)

    root.mainloop()

create_gui()



















